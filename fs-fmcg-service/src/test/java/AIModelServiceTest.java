import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.model.*;
import com.facishare.fmcg.api.service.ai.model.AIModelService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Test;

/**
 * AI模型服务测试类
 */
public class AIModelServiceTest extends TestBase {

    @Resource
    private AIModelService aiModelService;

    @Test
    public void testGetAIModelScenes() {
        // 测试获取AI模型场景列表
        ApiArg<GetAIModelScenes.Arg> arg = new ApiArg<>();
        GetAIModelScenes.Arg data = new GetAIModelScenes.Arg();
        arg.setData(data);
        arg.setTenantId(93947);  // 使用测试租户ID

        GetAIModelScenes.Result result = aiModelService.getAIModelScenes(arg).getData();
        System.out.println("场景列表：" + JSON.toJSONString(result));
    }

    @Test
    public void testGetAIModelsByScene() {
        // 测试根据场景获取模型列表
        ApiArg<GetAIModelsByScene.Arg> arg = new ApiArg<>();
        GetAIModelsByScene.Arg data = new GetAIModelsByScene.Arg();
        data.setScene("storeFront");  // 测试陈列场景
        arg.setData(data);
        arg.setTenantId(83921);

        GetAIModelsByScene.Result result = aiModelService.getAIModelsByScene(arg).getData();
        System.out.println("模型列表：" + JSON.toJSONString(result, SerializerFeature.DisableCircularReferenceDetect));
    }

    @Test
    public void testGetModelById() {
        // 测试获取模型详情
        ApiArg<GetModelById.Arg> arg = new ApiArg<>();
        GetModelById.Arg data = new GetModelById.Arg();
        data.setModelId("67bd947527c704000104949d");  // 使用测试模型ID
        data.setNeedObjectMap(true);
        arg.setData(data);
        arg.setTenantId(83921);

        GetModelById.Result result = aiModelService.getModelById(arg).getData();
        System.out.println("模型详情：" + JSON.toJSONString(result));
    }

    @Test
    public void testModelSwitch() {
        // 测试模型开关
        //testModelSwitchWithOperation(84931,"6788d5b3b6ab2619671a9a70",1);  // 测试开启
        testModelSwitchWithOperation(84931,"6788d5b3b6ab2619671a9a70",0);  // 测试关闭
    }

    private void testModelSwitchWithOperation(Integer tenantId, String modelId, Integer operation) {
        ApiArg<ModelSwitch.Arg> arg = new ApiArg<>();
        ModelSwitch.Arg data = new ModelSwitch.Arg();
        data.setModelId(modelId);
        data.setOperation(operation);
        arg.setData(data);
        arg.setTenantId(tenantId);

        ModelSwitch.Result result = aiModelService.modelSwitch(arg).getData();
        System.out.println("模型开关(operation=" + operation + ")结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testFullProcess() {
        // 测试完整流程
        System.out.println("=== 开始测试AI模型完整流程 ===");

        // 1. 获取场景列表
        testGetAIModelScenes();

        // 2. 获取指定场景的模型列表
        testGetAIModelsByScene();

        // 3. 获取模型详情
        testGetModelById();

        // 4. 测试模型开关
        testModelSwitch();

        System.out.println("=== AI模型完整流程测试结束 ===");
    }

    @Test
    public void testGetModelDescribe() {
        // 测试获取模型描述信息
        ApiArg<GetModelDescribe.Arg> arg = new ApiArg<>();
        GetModelDescribe.Arg data = new GetModelDescribe.Arg();
        arg.setData(data);
        arg.setTenantId(89321);  // 使用测试租户ID

        GetModelDescribe.Result result = aiModelService.getModelDescribe(arg).getData();
        System.out.println("模型描述信息：" + JSON.toJSONString(result));

        // 验证厂商信息
        System.out.println("支持的厂商数量：" + result.getModelManufacturer().size());
        result.getModelManufacturer().forEach(manufacturer ->
                System.out.println("厂商：" + manufacturer.getName() + ", 值：" + manufacturer.getValue()));

        // 验证模型类型
        System.out.println("支持的模型类型数量：" + result.getType().size());
        result.getType().forEach(type ->
                System.out.println("类型：" + type.getName() + ", 值：" + type.getValue()));
    }

    @Test
    public void testAddModel() {
        // 测试添加AI模型
        ApiArg<AddModel.Arg> arg = new ApiArg<>();
        AddModel.Arg data = JSON.parseObject("{\"model\":{\"status\":0,\"description\":\"\",\"confidence\":\"0.1\",\"modelManufacturer\":\"rio\",\"name\":\"3\",\"type\":\"OBJECT_RECOGNITION\",\"token_token\":\"d604f604d8ff1ff9bb8087d52be77f4d\",\"params\":{\"\":\"\"}},\"isUpdateToken\":false}", AddModel.Arg.class);
       /* AddModel.Arg data = new AddModel.Arg();
        // 设置测试数据
        ModelDTO modelDTO = new ModelDTO();
        modelDTO.setName("测试模型");
        modelDTO.setScene("display");
        modelDTO.setModelManufacturer("rio");
        modelDTO.setToken_token("423423423");
        modelDTO.setPlatform("rio");
        modelDTO.setTenantId(84931);
        modelDTO.setType("OBJECT_RECOGNITION");
        modelDTO.setConfidence(new BigDecimal("0.85"));
        data.setModel(modelDTO);  // 使用ModelDTO对象设置模型数据*/
        arg.setData(data);
        arg.setTenantId(83921);
        arg.setUserId(1000);

        ApiResult<AddModel.Result> result = aiModelService.addModel(arg);
        System.out.println("添加模型结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testUpdateModel() {
        // 测试更新AI模型
        ApiArg<UpdateModel.Arg> arg = new ApiArg<>();
        UpdateModel.Arg data = new UpdateModel.Arg();
        // 设置测试数据
        ModelDTO modelDTO = JSON.parseObject("{\"createTime\":*************,\"creator\":{\"id\":-10000,\"name\":\"系统\"},\"id\":\"63d8b905d3245cdbd1f0e253\",\"key\":\"zhenxinguantou\",\"lastModifier\":{\"id\":-10000,\"name\":\"系统\"},\"lastModifyTime\":*************,\"modelManufacturer\":\"baidu\",\"name\":\"百度SKU模型-真心罐头-copy\",\"params\":{\"split_shelf\":false},\"platform\":\"baidu\",\"scene\":\"display\",\"status\":1,\"tenantId\":84931,\"tokenInfo\":{\"account\":\"1******7\",\"appId\":\"7******7\",\"appKey\":\"4******Z\",\"identityKey\":\"ZHEN_XIN_SP\",\"key\":\"Z******P\",\"secretKey\":\"J******8\",\"type\":\"ZHEN_XIN_SP\"},\"token_account\":\"1******7\",\"token_appId\":\"7******7\",\"token_appKey\":\"4******Z\",\"token_identityKey\":\"ZHEN_XIN_SP\",\"token_key\":\"Z******P\",\"token_secretKey\":\"J******8\",\"token_type\":\"ZHEN_XIN_SP\",\"type\":\"OBJECT_RECOGNITION\"}", ModelDTO.class);
        modelDTO.setName("更新后的模型名称");
        modelDTO.setScene("display");
        data.setModel(modelDTO);  // 使用ModelDTO对象设置模型数据
        data.setUpdateToken(true);
        arg.setData(data);
        arg.setTenantId(84931);

        ApiResult<UpdateModel.Result> result = aiModelService.updateModel(arg);
        System.out.println("更新模型结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testQueryObjectList() {
        // 测试查询对象映射列表
        ApiArg<QueryObjectList.Arg> arg = new ApiArg<>();
        QueryObjectList.Arg data = new QueryObjectList.Arg();
        // 设置测试数据
        data.setModelId("659fd13034c9dc46a1fdeae9");

        arg.setData(data);
        arg.setTenantId(83921);

        QueryObjectList.Result result = aiModelService.queryObjectList(arg).getData();
        System.out.println("查询对象映射列表结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testBatchDeleteObjectMap() {
        // 测试批量删除对象映射
        ApiArg<BatchDeleteObjectMap.Arg> arg = new ApiArg<>();
        BatchDeleteObjectMap.Arg data = new BatchDeleteObjectMap.Arg();
        // 设置测试数据
        data.setIds(Arrays.asList("67ecffd6ac3a82000192fc5b"));

        arg.setData(data);
        arg.setTenantId(83921);

        BatchDeleteObjectMap.Result result = aiModelService.batchDeleteObjectMap(arg).getData();
        System.out.println("批量删除对象映射结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testBatchSaveOrUpdateObjectMap() {
        // 测试批量保存或更新对象映射
        ApiArg<BatchSaveOrUpdateObjectMap.Arg> arg = new ApiArg<>();
        BatchSaveOrUpdateObjectMap.Arg data = new BatchSaveOrUpdateObjectMap.Arg();
        String modelId = "67cfd6b0ab372c0001d5be9a";
        Integer tenantId = 83921;
        // 这里需要根据实际的对象映射结构来设置数据
        List<ObjectMapDTO> objectMaps = new ArrayList<>();
        // 添加测试数据
        ObjectMapDTO map = new ObjectMapDTO();
        map.setModelId(modelId);
        map.setTenantId(tenantId);
        map.setObjectId("659fd22222b4c400010cceba");
        map.setApiName("ProductObj");
        map.setKey("posm_rio_5du_bazhaotie");
        map.setUnitType("small");
        objectMaps.add(map);

        data.setObjectMaps(objectMaps);
        arg.setData(data);
        arg.setTenantId(tenantId);

        ApiResult<BatchSaveOrUpdateObjectMap.Result> result = aiModelService.batchSaveOrUpdateObjectMap(arg);
        System.out.println("批量保存或更新对象映射结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testExtendedFullProcess() {
        // 测试扩展的完整流程
        System.out.println("=== 开始测试AI模型扩展功能完整流程 ===");

        // 1. 添加新模型
        testAddModel();

        // 2. 更新模型
        testUpdateModel();

        // 3. 查询对象映射列表
        testQueryObjectList();

        // 4. 批量保存对象映射
        testBatchSaveOrUpdateObjectMap();

        // 5. 批量删除对象映射
        testBatchDeleteObjectMap();

        System.out.println("=== AI模型扩展功能完整流程测试结束 ===");
    }

    @Test
    public void testQueryRuleList() {
        // 测试查询规则列表
        ApiArg<QueryRuleList.Arg> arg = new ApiArg<>();
        QueryRuleList.Arg data = new QueryRuleList.Arg();
        // 设置测试数据
        data.setModelId("68b8fc07f99eb30001110986");  // 使用测试模型ID
        arg.setData(data);
        arg.setTenantId(83921);  // 使用测试租户ID

        QueryRuleList.Result result = aiModelService.queryRuleList(arg).getData();
        System.out.println("查询规则列表结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testBatchQueryModel() {
        // 测试批量查询AI模型
        ApiArg<BatchQueryModel.Arg> arg = new ApiArg<>();
        BatchQueryModel.Arg data = new BatchQueryModel.Arg();

        // 设置测试数据
        List<BatchQueryModel.QueryParam> queryParams = new ArrayList<>();
        BatchQueryModel.QueryParam queryParam1 = new BatchQueryModel.QueryParam();
        queryParam1.setModelId("12345124");
        queryParam1.setNeedObjectMap(true);
        queryParams.add(queryParam1);

        BatchQueryModel.QueryParam queryParam2 = new BatchQueryModel.QueryParam();
        queryParam2.setModelId("56789012");
        queryParam2.setNeedObjectMap(false);
        queryParams.add(queryParam2);

        data.setQueryParams(queryParams);
        arg.setData(data);
        arg.setTenantId(84293);// 使用测试租户ID
        arg.setUserId(1000);

        BatchQueryModel.Result result = aiModelService.batchQueryModel(arg).getData();
        System.out.println("批量查询AI模型结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testBatchQueryAIRuleByIds() {
        // 测试批量查询AI规则
        ApiArg<BatchQueryAIRuleByIds.Arg> arg = new ApiArg<>();
        BatchQueryAIRuleByIds.Arg data = new BatchQueryAIRuleByIds.Arg();

        // 设置测试数据
        List<String> queryIds = Arrays.asList("1&&67bc2997c1e320241f920d65");
        data.setQueryIds(queryIds);
        arg.setData(data);
        arg.setTenantId(84931);  // 使用测试租户ID
        arg.setUserId(1000);
        BatchQueryAIRuleByIds.Result result = aiModelService.batchQueryAIRuleByIds(arg).getData();
        System.out.println("批量查询AI规则结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testSaveOrUpdateDetectRule() {
        // 测试保存或更新检测规则
        ApiArg<SaveOrUpdateDetectRule.Arg> arg = new ApiArg<>();
        SaveOrUpdateDetectRule.Arg data = new SaveOrUpdateDetectRule.Arg();
        
        // 设置测试数据
        AIDetectRuleDTO ruleDTO = new AIDetectRuleDTO();
        ruleDTO.setId(null);  // 设置规则ID
        ruleDTO.setModelId("68b8fc07f99eb30001110986");
        ruleDTO.setName("测试检测规则");
        ruleDTO.setRuleDescribe("这是一个测试用的检测规则");
        ruleDTO.setTenantId(83921);
        
          /**
     * 业务能力
     * isOpenProductRowNumber 商品sku/排面数识别
     * isOpenGroupNumber 商品陈列组数
     * isOpenLayerNumber 货架层数识别
     * openSkuUnit 商品单位识别
     * isOpenPrices 价格识别
     * isOpenSceneDetect 商品陈列场景
     * isOpenDisplayForm 商品陈列形式
     * isPOSMDetect 物料识别
     */
        Map<String, Integer> detectCapabilityMap = new HashMap<>();
//        detectCapabilityMap.put("isOpenProductRowNumber", 1);
//        detectCapabilityMap.put("isOpenGroupNumber", 1);
//        detectCapabilityMap.put("isOpenLayerNumber", 1);
//        detectCapabilityMap.put("openSkuUnit", 1);
//        detectCapabilityMap.put("isOpenPrices", 1);
//        detectCapabilityMap.put("isOpenSceneDetect", 1);
//        detectCapabilityMap.put("isOpenDisplayForm", 1);
        detectCapabilityMap.put("isOpenDisplayLayerDetect", 1);
        ruleDTO.setDetectCapabilityMap(detectCapabilityMap);

         /**
     * aiPath ai图片
     * productName 产品名称
     * aiRowNumber 商品排面数  需要calculateType属性
     * aiGroupNumber 商品组数  需要calculateType属性
     * aiLayerNumber 层数
     * aiSceneField 场景字段
     * **/
        // 设置字段映射
        Map<String, FieldDTO> fieldMap = new HashMap<>();
        FieldDTO fieldDTO = new FieldDTO();
        fieldDTO.setType("mapping");
        fieldDTO.setFieldKey("displayValidLayerNumber");
        fieldDTO.setCalculateType(0);
        fieldMap.put("displayValidLayerNumber", fieldDTO);
        fieldDTO.setEnable(true);
        ValidLayerJudgmentDTO validLayerJudgmentDTO = new ValidLayerJudgmentDTO();
        validLayerJudgmentDTO.setKey("purity");
        validLayerJudgmentDTO.setValue(90.9);
        fieldDTO.setValidLayerJudgment(validLayerJudgmentDTO);

        ruleDTO.setFieldMap(fieldMap);

        data.setAiDetectRuleDTO(ruleDTO);
        arg.setData(data);
        arg.setTenantId(83921);
        arg.setUserId(1000);

        SaveOrUpdateDetectRule.Result result = aiModelService.saveOrUpdateDetectRule(arg).getData();
        System.out.println("保存或更新检测规则结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testDeleteDetectRule() {
        // 测试删除检测规则
        ApiArg<DeleteDetectRule.Arg> arg = new ApiArg<>();
        DeleteDetectRule.Arg data = new DeleteDetectRule.Arg();
        
        // 设置测试数据
        data.setRuleId("67bc2997c1e320241f920d65"); // 使用setDetectRuleId替代setRuleId
        
        arg.setData(data);
        arg.setTenantId(84931);
        arg.setUserId(1000);

        DeleteDetectRule.Result result = aiModelService.deleteDetectRule(arg).getData();
        System.out.println("删除检测规则结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testGetDisplayScenesByModelId() {
        // 测试根据模型ID获取场景列表
        ApiArg<GetDisplayScenesByModelId.Arg> arg = new ApiArg<>();
        GetDisplayScenesByModelId.Arg data = new GetDisplayScenesByModelId.Arg();
        data.setModelId("67cfd6b0ab372c0001d5be9a");  // 使用测试模型ID
        arg.setData(data);
        arg.setTenantId(83921);  // 使用测试租户ID

        ApiResult<GetDisplayScenesByModelId.Result> result = aiModelService.getDisplayScenesByModelId(arg);
        System.out.println("获取场景列表结果：" + JSON.toJSONString(result));
        
        // 验证结果
        if (result.isSuccess()) {
            System.out.println("场景列表数量: " + (result.getData().getDisplayScenes() != null ? result.getData().getDisplayScenes().size() : 0));
            if (result.getData().getDisplayScenes() != null && !result.getData().getDisplayScenes().isEmpty()) {
                System.out.println("场景列表详情: " + JSON.toJSONString(result.getData().getDisplayScenes()));
            }
        }
    }

    @Test
    public void testAddToken() {
        // 测试添加Token信息
        ApiArg<AddToken.Arg> arg = new ApiArg<>();
        AddToken.Arg data = new AddToken.Arg();
        
        // 创建Token信息
        TokenInfoDTO tokenInfo = new TokenInfoDTO();
        tokenInfo.setIdentityKey("test_token_" + System.currentTimeMillis());
        tokenInfo.setType("baidu");
        tokenInfo.setAk("test_ak");
        tokenInfo.setSk("test_sk");
        tokenInfo.setHost("https://aip.baidubce.com");
        tokenInfo.setAppKey("test_app_key");
        tokenInfo.setSecretKey("test_secret_key");
        
        data.setTokenInfo(tokenInfo);
        arg.setData(data);
        arg.setTenantId(83921);

        AddToken.Result result = aiModelService.addToken(arg).getData();
        System.out.println("添加Token结果：" + JSON.toJSONString(result));
    }
} 