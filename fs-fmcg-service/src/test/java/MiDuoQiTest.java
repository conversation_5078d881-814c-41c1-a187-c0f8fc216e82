import com.facishare.fmcg.vision.client.MiDuoQiMarketingDetectClient;
import com.facishare.fmcg.vision.client.model.MiDuoQiMarketingDetectRequest;
import com.facishare.fmcg.vision.client.model.MiDuoQiMarketingDetectResponse;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Objects;

public class MiDuoQiTest extends TestBase {

    @Resource
    private MiDuoQiMarketingDetectClient miDuoQiMarketingDetectClient;

    @Test
    public void detectTest() {
        MiDuoQiMarketingDetectRequest request = new MiDuoQiMarketingDetectRequest();
        request.setImages(Lists.newArrayList("N_202508_06_449a9155dbaa415981f42bbf6ab6500f"));
        MiDuoQiMarketingDetectResponse response = miDuoQiMarketingDetectClient.completions( request);

        assert Objects.nonNull(response);
    }
}
