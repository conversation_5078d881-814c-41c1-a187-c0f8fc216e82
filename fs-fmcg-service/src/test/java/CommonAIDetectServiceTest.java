import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.detect.CommonAIDetect;
import com.facishare.fmcg.api.service.ai.detect.CommonAIDetectService;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;

/**
 * 门头识别服务测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
public class CommonAIDetectServiceTest extends TestBase {

    @Resource
    private CommonAIDetectService commonAIDetectService;

    /**
     * 测试门头识别正向流程
     */
    @Test
    public void testCommonAIDetectSuccess() {
        // 准备测试数据
        ApiArg<CommonAIDetect.Arg> arg = new ApiArg<>();
        arg.setTenantId(83921); // 使用测试租户ID
        arg.setUserId(1000);
        arg.setTenantAccount("83921");

        CommonAIDetect.Arg data = new CommonAIDetect.Arg();
        data.setNPath("N_202508_19_0a6ac0be18614582ad23357565ea9989");
        data.setModelId("68a6e4cfa7dde80001a2aa47"); // 使用测试模型ID
        data.setRuleId("68a6e4d0a7dde80001a2aa4a"); // 使用测试规则ID
        data.setScene("storeFront");
        
        // 设置额外参数
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("realStoreName", "咿呀嘿");
        data.setExtraParams(extraParams);

        arg.setData(data);

        // 执行测试
        ApiResult<CommonAIDetect.Result> result = commonAIDetectService.commonAIDetect(arg);

        // 验证结果
        Assert.assertNotNull("返回结果不应为空", result);
        System.out.println("门头识别测试结果: " + JSON.toJSONString(result));
        
        // 如果成功，验证返回数据
        if (result.getCode() == 0) {
            Assert.assertNotNull("返回数据不应为空", result.getData());
            Assert.assertNotNull("检测详情不应为空", result.getData().getDetectDetail());
            
            // 打印检测详情
            JSONObject detectDetail = result.getData().getDetectDetail();
            System.out.println("检测详情: " + JSON.toJSONString(detectDetail, true));
        } else {
            System.out.println("识别失败，错误信息: " + result.getMessage());
        }
    }

} 