<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:component-scan base-package="com.facishare.fmcg,com.facishare.paas.pod"/>
    <context:annotation-config/>

    <import resource="classpath:spring/cms.xml"/>
    <import resource="classpath:spring/ei-ea-converter.xml"/>
    <import resource="classpath:spring/service.xml"/>
    <import resource="classpath:spring/schedule.xml"/>
    <import resource="classpath:fs-social-api.xml"/>
    <import resource="classpath:spring/fmcg-vision-client.xml"/>
    <import resource="classpath:META-INF/provider.xml"/>
</beans>
