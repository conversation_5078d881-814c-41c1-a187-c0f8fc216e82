{"components": [{"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "product_id"}, {"is_readonly": true, "is_required": false, "render_type": "select_one", "field_name": "unit"}, {"is_readonly": true, "is_required": false, "render_type": "currency", "field_name": "price"}, {"is_readonly": true, "is_required": false, "render_type": "object_reference", "field_name": "activity_product_id"}, {"is_readonly": false, "is_required": true, "render_type": "number", "field_name": "activity_cashing_quantity"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "activity_cashing_total_price"}, {"is_readonly": false, "is_required": false, "render_type": "number", "field_name": "cost_cashing_quantity"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "cost_cashing_total_price"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "text", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_3yK46__c", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "isSticky": false, "grayLimit": 1, "order": 3}], "ref_object_api_name": "TPMDealerActivityCashingProductObj", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "default_component": "form_component", "layout_structure": {"layout": [{"components": [[]], "columns": [{"width": "100%"}]}, {"components": [["form_component"]], "columns": [{"width": "auto"}]}], "flow_ui_version": 820, "layout_structure_type": 1}, "buttons": [], "package": "CRM", "used_info": "", "display_name": "申请核销兑付产品流程布局", "is_default": false, "version": 6, "api_name": "layout_flow_default_TPMDealerActivityCashingProductObj__c", "namespace": "flow", "layout_description": ""}