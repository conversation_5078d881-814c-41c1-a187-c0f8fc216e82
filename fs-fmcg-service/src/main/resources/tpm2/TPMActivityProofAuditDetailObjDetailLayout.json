{
  "components": [
    {
      "field_section": [],
      "buttons": [],
      "api_name": "relevant_team_component",
      "related_list_name": "",
      "header": "相关团队",
      "nameI18nKey": "paas.udobj.constant.relevant_team",
      "type": "user_list"
    },
    {
      "field_section": [],
      "buttons": [],
      "api_name": "sale_log",
      "related_list_name": "",
      "header": "跟进动态",
      "nameI18nKey": "paas.udobj.follow_up_dynamic",
      "type": "related_record"
    },
    {
      "field_section": [],
      "buttons": [],
      "api_name": "operation_log",
      "related_list_name": "",
      "is_hidden": false,
      "header": "修改记录",
      "nameI18nKey": "paas.udobj.modify_log",
      "type": "related_record",
      "order": 1
    }
    {
      "components": [
        [
          "form_component"
        ],
        [
          "operation_log"
        ]
      ],
      "buttons": [],
      "api_name": "container_default_layout_default_TPMActivityProofAuditDetailObj__c",
      "tabs": [
        {
          "api_name": "tab_form_component",
          "header": "详细信息",
          "nameI18nKey": "paas.udobj.detail_info"
        },
        {
          "api_name": "tab_operation_log",
          "header": "修改记录",
          "nameI18nKey": "paas.udobj.modify_log"
        }
      ],
      "header": "页签容器",
      "type": "tabs"
    },
    {
      "field_section": [],
      "buttons": [],
      "api_name": "head_info",
      "related_list_name": "",
      "header": "标题和按钮",
      "nameI18nKey": "paas.udobj.head_info",
      "exposedButton": 3,
      "type": "simple"
    },
    {
      "field_section": [
        {
          "render_type": "employee",
          "field_name": "owner"
        },
        {
          "render_type": "text",
          "field_name": "owner_department"
        },
        {
          "render_type": "date_time",
          "field_name": "last_modified_time"
        },
        {
          "render_type": "record_type",
          "field_name": "record_type"
        }
      ],
      "buttons": [],
      "api_name": "top_info",
      "related_list_name": "",
      "header": "摘要信息",
      "type": "top_info"
    },
    {
      "field_section": [
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "audit_item"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "proof_detail_cost_standard"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "amount"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "subtotal"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "number",
              "field_name": "audit_amount"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "currency",
              "field_name": "audit_subtotal"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "activity_proof_detail_id"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "activity_agreement_detail_id"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "activity_detail_id"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "activity_item_id"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "type"
            }
          ],
          "api_name": "base_field_section__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "基本信息",
          "is_show": true
        },
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "auto_number",
              "field_name": "name"
            },
            {
              "is_readonly": false,
              "is_required": true,
              "render_type": "master_detail",
              "field_name": "activity_proof_audit_id"
            },
            {
              "is_readonly": false,
              "is_required": true,
              "render_type": "record_type",
              "field_name": "record_type"
            },
            {
              "is_readonly": false,
              "is_required": true,
              "render_type": "employee",
              "field_name": "owner"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "owner_department"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "employee",
              "field_name": "created_by"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "date_time",
              "field_name": "create_time"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "employee",
              "field_name": "last_modified_by"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "date_time",
              "field_name": "last_modified_time"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "department",
              "field_name": "data_own_department"
            },
            {
              "is_readonly": false,
              "is_required": true,
              "is_tiled": false,
              "render_type": "select_one",
              "field_name": "life_status"
            }
          ],
          "api_name": "group_Y216y__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "系统信息",
          "is_show": true
        }
      ],
      "buttons": [],
      "api_name": "form_component",
      "related_list_name": "",
      "column": 2,
      "is_hidden": false,
      "header": "详细信息",
      "nameI18nKey": "paas.udobj.detail_info",
      "type": "form",
      "order": 2
    }
  ],
  "ref_object_api_name": "TPMActivityProofAuditDetailObj",
  "layout_type": "detail",
  "hidden_buttons": [
    "Edit_button_default",
    "SaleRecord_button_default",
    "Dial_button_default",
    "ChangeOwner_button_default",
    "StartBPM_button_default",
    "Abolish_button_default",
    "StartStagePropellor_button_default",
    "Lock_button_default",
    "Unlock_button_default",
    "Clone_button_default",
    "SendMail_button_default",
    "Discuss_button_default",
    "Remind_button_default",
    "Schedule_button_default",
    "Print_button_default"
  ],
  "ui_event_ids": [],
  "is_deleted": false,
  "default_component": "form_component",
  "layout_structure": {
    "layout": [
      {
        "components": [
          [
            "head_info"
          ]
        ],
        "columns": [
          {
            "width": "100%"
          }
        ]
      },
      {
        "components": [
          [
            "top_info",
            "container_default_layout_default_TPMActivityProofAuditDetailObj__c"
          ],
          [
            "relevant_team_component",
            "sale_log"
          ]
        ],
        "columns": [
          {
            "width": "auto"
          },
          {
            "width": "500px",
            "retractable": true
          }
        ]
      }
    ]
  },
  "buttons": [],
  "package": "CRM",
  "display_name": "默认布局",
  "is_default": true,
  "version": 3,
  "api_name": "layout_default_TPMActivityProofAuditDetailObj__c",
  "layout_description": ""
}