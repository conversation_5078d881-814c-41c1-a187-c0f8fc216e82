delete from mt_field where (tenant_id, describe_id ) in (select tenant_id, describe_id from mt_describe where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj');
delete from mt_action where (tenant_id, describe_id ) in (select tenant_id, describe_id from mt_describe where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj');
delete from mt_cluster where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj';
delete from mt_udef_action where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj';
delete from mt_udef_button where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj';
delete from mt_ui_component where tenant_id = '84931' and ref_object_api_name = 'TPMBudgetAccountDetailObj';
delete from mt_layout_rule where tenant_id = '84931' and object_describe_api_name = 'TPMBudgetAccountDetailObj';
delete from mt_rules where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj';
delete from mt_custom_search_template where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj';
delete from mt_ui_event where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj';
delete from mt_unique_rule where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj';
delete from mt_validate_rule where (tenant_id, describe_id ) in (select tenant_id, describe_id from mt_describe where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj');
delete from mt_describe where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountDetailObj';

delete from mt_field where (tenant_id, describe_id ) in (select tenant_id, describe_id from mt_describe where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj');
delete from mt_action where (tenant_id, describe_id ) in (select tenant_id, describe_id from mt_describe where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj');
delete from mt_cluster where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj';
delete from mt_udef_action where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj';
delete from mt_udef_button where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj';
delete from mt_ui_component where tenant_id = '84931' and ref_object_api_name = 'TPMBudgetAccountObj';
delete from mt_layout_rule where tenant_id = '84931' and object_describe_api_name = 'TPMBudgetAccountObj';
delete from mt_rules where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj';
delete from mt_custom_search_template where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj';
delete from mt_ui_event where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj';
delete from mt_unique_rule where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj';
delete from mt_validate_rule where (tenant_id, describe_id ) in (select tenant_id, describe_id from mt_describe where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj');
delete from mt_describe where tenant_id = '84931' and describe_api_name = 'TPMBudgetAccountObj';