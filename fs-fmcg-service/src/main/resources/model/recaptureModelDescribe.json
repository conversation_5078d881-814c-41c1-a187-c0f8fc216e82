{"modelManufacturer": [{"name": "纷享", "value": "fs", "i18nKey": "fmcg.model.manufacturer.fs", "types": ["IMAGE_CLASSIFICATION"]}, {"name": "百度", "value": "baidu", "i18nKey": "fmcg.model.manufacturer.baidu", "types": ["OBJECT_RECOGNITION"]}], "type": [{"name": "翻拍识别模型", "value": "IMAGE_CLASSIFICATION", "i18nKey": "fmcg.model.type.IMAGE_CLASSIFICATION", "tip": "", "tipI18nKey": "fmcg.model.type.IMAGE_CLASSIFICATION.tip"}], "modelInfoMap": {"fs": [{"type": "IMAGE_CLASSIFICATION", "field": []}], "baidu": [{"type": "IMAGE_CLASSIFICATION", "field": [{"apiName": "key", "label": "模型唯一键（key）", "i18nKey": "fmcg.model.field.token_key", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_key.tip", "type": "text"}, {"apiName": "token_appKey", "label": "appKey", "i18nKey": "fmcg.model.field.token_appKey", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_appKey.tip", "type": "text"}, {"apiName": "token_secretKey", "label": "secret<PERSON>ey", "i18nKey": "fmcg.model.field.token_secretKey", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_secretKey.tip", "type": "text"}]}]}}