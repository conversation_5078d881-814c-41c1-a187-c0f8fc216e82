{"modelManufacturer": [{"name": "百度", "value": "baidu", "i18nKey": "fmcg.model.manufacturer.baidu", "types": ["OBJECT_RECOGNITION"]}, {"name": "图匠", "value": "tu_jiang", "i18nKey": "fmcg.model.manufacturer.tu_jiang", "types": ["OBJECT_RECOGNITION"]}, {"name": "商汤", "value": "sense_time", "i18nKey": "fmcg.model.manufacturer.sense_time", "types": ["OBJECT_RECOGNITION"]}, {"name": "华为", "value": "hua<PERSON>", "i18nKey": "fmcg.model.manufacturer.huawei", "types": ["OBJECT_RECOGNITION"]}, {"name": "RIO", "value": "rio", "i18nKey": "fmcg.model.manufacturer.rio", "types": ["OBJECT_RECOGNITION"]}, {"name": "元气森林", "value": "yqsl", "i18nKey": "fmcg.model.manufacturer.yqsl", "types": ["OBJECT_RECOGNITION"]}, {"name": "蒙牛", "value": "meng_niu", "i18nKey": "fmcg.model.manufacturer.mengniu", "types": ["OBJECT_RECOGNITION"]}], "type": [{"name": "商品检测模型", "value": "OBJECT_RECOGNITION", "i18nKey": "fmcg.model.type.object_recognition", "tip": "适用于货架、端架、挂架等场景的商品陈列识别，支持识别商品sku及排面数，陈列顺序、层数、场景，统计排名数量和占比等", "tipI18nKey": "fmcg.model.type.object_recognition.tip"}], "modelInfoMap": {"baidu": [{"type": "OBJECT_RECOGNITION", "field": [{"apiName": "key", "label": "模型唯一键（key）", "i18nKey": "fmcg.model.field.token_key", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_key.tip", "type": "text"}, {"apiName": "token_appKey", "label": "appKey", "i18nKey": "fmcg.model.field.token_appKey", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_appKey.tip", "type": "text"}, {"apiName": "token_secretKey", "label": "secret<PERSON>ey", "i18nKey": "fmcg.model.field.token_secretKey", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_secretKey.tip", "type": "text"}]}], "huawei": [{"type": "OBJECT_RECOGNITION", "field": [{"apiName": "token_userName", "label": "userName", "i18nKey": "fmcg.model.field.token_userName", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_userName.tip", "type": "text"}, {"apiName": "token_password", "label": "password", "i18nKey": "fmcg.model.field.token_password", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_password.tip", "type": "text"}, {"apiName": "token_domainName", "label": "domainName", "i18nKey": "fmcg.model.field.token_domainName", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_domainName.tip", "type": "text"}, {"apiName": "token_projectName", "label": "projectName", "i18nKey": "fmcg.model.field.token_projectName", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_projectName.tip", "type": "text"}]}], "sense_time": [{"type": "OBJECT_RECOGNITION", "field": [{"apiName": "token_ak", "label": "ak", "i18nKey": "fmcg.model.field.token_ak", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_ak.tip", "type": "text"}, {"apiName": "token_sk", "label": "sk", "i18nKey": "fmcg.model.field.token_sk", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_sk.tip", "type": "text"}, {"apiName": "token_host", "label": "host", "i18nKey": "fmcg.model.field.token_host", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_host.tip", "type": "text"}]}], "yqsl": [{"type": "OBJECT_RECOGNITION", "field": [{"apiName": "platform", "label": "平台", "i18nKey": "fmcg.model.field.platform", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.platform.tip", "type": "text"}, {"apiName": "token_appId", "label": "appId", "i18nKey": "fmcg.model.field.token_appId", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_appId.tip", "type": "text"}, {"apiName": "token_key", "label": "key", "i18nKey": "fmcg.model.field.token_key1", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_key1.tip", "type": "text"}]}], "tu_jiang": [{"type": "OBJECT_RECOGNITION", "field": [{"apiName": "token_account", "label": "account", "i18nKey": "fmcg.model.field.token_account", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_account.tip", "type": "text"}, {"apiName": "token_password", "label": "password", "i18nKey": "fmcg.model.field.token_password", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_password.tip", "type": "text"}, {"apiName": "token_compCode", "label": "compCode", "i18nKey": "fmcg.model.field.token_compCode", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_compCode.tip", "type": "text"}, {"apiName": "token_secretKey", "label": "secret<PERSON>ey", "i18nKey": "fmcg.model.field.token_secretKey", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_secretKey.tip", "type": "text"}, {"apiName": "token_url", "label": "url", "i18nKey": "fmcg.model.field.token_url", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_url.tip", "type": "text"}]}], "rio": [{"type": "OBJECT_RECOGNITION", "field": [{"apiName": "token_token", "label": "token", "i18nKey": "fmcg.model.field.token_token", "required": true, "tip": "", "tipI18nKey": "fmcg.model.field.token_token.tip", "type": "text"}]}], "meng_niu": [{"type": "OBJECT_RECOGNITION", "field": [{"apiName": "platform", "label": "平台", "i18nKey": "fmcg.model.field.platform", "required": false, "tip": "", "tipI18nKey": "fmcg.model.field.platform.tip", "type": "text"}]}]}}