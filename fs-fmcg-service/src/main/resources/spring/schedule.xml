<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
          http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

    <bean id="correctTPMActivityStatusWorker"
          class="com.facishare.fmcg.service.schedule.CorrectTPMActivityStatusWorker"/>
    <bean id="uniqueMongoIdResetWorker"
          class="com.facishare.fmcg.service.schedule.UniqueMongoIdResetWorker"/>
    <bean id="presetBudgetWorker"
          class="com.facishare.fmcg.service.schedule.PresetTPMBudgetWorker"/>
    <bean id="redPacketRefreshWorker"
          class="com.facishare.fmcg.service.schedule.RedPacketRefreshWorker"/>
    <bean id="budgetProvisionOccupyWorker"
          class="com.facishare.fmcg.service.schedule.BudgetProvisionOccupyWorker"/>
    <bean id="correctTPMProofTimePeriodStatusWorker"
          class="com.facishare.fmcg.service.schedule.CorrectTPMProofTimePeriodStatusWorker"/>

    <task:scheduler id="scheduler" pool-size="5"/>
    <task:scheduled-tasks scheduler="scheduler">
        <task:scheduled ref="correctTPMActivityStatusWorker" method="run" cron="0 0 0,5,12,17,21 * * ?"/>
        <task:scheduled ref="uniqueMongoIdResetWorker" method="run" cron="0 1 23,0 * 3,4 ?"/>
        <task:scheduled ref="redPacketRefreshWorker" method="run" cron="1 0 0 * * ? "/>
        <task:scheduled ref="budgetProvisionOccupyWorker" method="run" cron="1 0 0 * * ? "/>
        <task:scheduled ref="correctTPMProofTimePeriodStatusWorker" method="run" cron="0 0 0 * * ?"/>
    </task:scheduled-tasks>
</beans>